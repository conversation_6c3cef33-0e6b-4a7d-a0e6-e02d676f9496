export default function RayChannelIcon(props: {
    color?: string
    size?: number
}) {
    const size = props.size ?? 23
    const circleRadius = 1.5
    const color = props.color ?? 'black'
    
    return (
        <svg
            width={size}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            {/* First ray - from anchor 1 to anchor 2 */}
            <line x1="4" y1="6" x2="20" y2="10" stroke={color} strokeWidth="1" />
            {/* Second ray - from anchor 3 to anchor 4 */}
            <line x1="4" y1="18" x2="20" y2="14" stroke={color} strokeWidth="1" />
            
            {/* Anchor points */}
            <circle cx="4" cy="6" r={circleRadius} stroke={color} fill="none" strokeWidth="1" />
            <circle cx="12" cy="8" r={circleRadius} stroke={color} fill="none" strokeWidth="1" />
            <circle cx="4" cy="18" r={circleRadius} stroke={color} fill="none" strokeWidth="1" />
            <circle cx="12" cy="16" r={circleRadius} stroke={color} fill="none" strokeWidth="1" />
        </svg>
    )
}
